// app/api/parse-pdf/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { AdvancedPDFParser } from '@/lib/advanced-pdf-parser';
import pdf from 'pdf-parse';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'PDF file is required' }, { status: 400 });
    }

    if (!file.name.endsWith('.pdf')) {
      return NextResponse.json({ error: 'Only PDF files are supported' }, { status: 400 });
    }

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Extract text from PDF
    const pdfData = await pdf(buffer, {
      normalizeWhitespace: true,
      disableCombineTextItems: false
    });

    const rawText = pdfData.text;

    if (!rawText || rawText.trim().length === 0) {
      return NextResponse.json({
        error: 'No text content found in PDF',
        details: 'The PDF might contain only images or be password-protected'
      }, { status: 400 });
    }

    // Parse the document using advanced parser
    const parsedDocument = AdvancedPDFParser.parseDocument(rawText);

    // Add raw text and metadata to the document
    const enhancedDocument = {
      ...parsedDocument,
      rawText,
      filename: file.name,
      fileSize: file.size,
      processedAt: new Date().toISOString()
    };

    // Validate required fields
    const missingFields: string[] = [];
    const suggestions: { [key: string]: string[] } = {};

    // Basic validation based on document type
    if (!parsedDocument.documentNumber && !parsedDocument.DeliveryChallan && !parsedDocument.IRN) {
      missingFields.push('Document Number');
      suggestions['Document Number'] = ['RSNT26T0001', 'RSNT26D0001', 'RSNT26J0001'];
    }

    return NextResponse.json({
      document: enhancedDocument,
      metadata: {
        numPages: pdfData.numpages,
        info: pdfData.info,
        filename: file.name,
        fileSize: file.size
      },
      missingFields,
      suggestions,
      success: true
    });

  } catch (error) {
    console.error('Error parsing PDF:', error);
    return NextResponse.json(
      {
        error: 'Failed to parse PDF',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}