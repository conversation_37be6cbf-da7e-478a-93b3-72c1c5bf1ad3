'use client';

import { useState } from 'react';
import { ParsedDocumentData } from '@/lib/advanced-pdf-parser';

interface ParsedDataViewerProps {
  data: ParsedDocumentData;
  viewMode: 'ui' | 'raw';
  onUpdate: (updatedData: ParsedDocumentData) => void;
}

export default function ParsedDataViewer({ data, viewMode, onUpdate }: ParsedDataViewerProps) {
  const [editingField, setEditingField] = useState<string | null>(null);
  const [editValue, setEditValue] = useState<string>('');

  const handleEdit = (fieldPath: string, currentValue: any) => {
    setEditingField(fieldPath);
    setEditValue(typeof currentValue === 'object' ? JSON.stringify(currentValue, null, 2) : String(currentValue || ''));
  };

  const handleSave = () => {
    if (!editingField) return;

    try {
      // Parse the field path and update the data
      const pathParts = editingField.split('.');
      const updatedData = { ...data };
      let current: any = updatedData;

      // Navigate to the parent object
      for (let i = 0; i < pathParts.length - 1; i++) {
        if (!current[pathParts[i]]) {
          current[pathParts[i]] = {};
        }
        current = current[pathParts[i]];
      }

      // Set the final value
      const finalKey = pathParts[pathParts.length - 1];
      try {
        // Try to parse as JSON first
        current[finalKey] = JSON.parse(editValue);
      } catch {
        // If not valid JSON, treat as string
        current[finalKey] = editValue;
      }

      onUpdate(updatedData);
      setEditingField(null);
      setEditValue('');
    } catch (error) {
      console.error('Error updating field:', error);
    }
  };

  const handleCancel = () => {
    setEditingField(null);
    setEditValue('');
  };

  const renderEditableField = (label: string, value: any, fieldPath: string) => {
    const isEditing = editingField === fieldPath;
    const displayValue = typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value || '');

    return (
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 hover:bg-gray-100 transition-colors">
        <div className="flex items-center justify-between mb-2">
          <label className="font-medium text-gray-700 text-sm">{label}</label>
          {!isEditing && (
            <button
              onClick={() => handleEdit(fieldPath, value)}
              className="text-blue-600 hover:text-blue-800 text-sm flex items-center space-x-1"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              <span>Edit</span>
            </button>
          )}
        </div>
        
        {isEditing ? (
          <div className="space-y-2">
            <textarea
              value={editValue}
              onChange={(e) => setEditValue(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md text-sm font-mono resize-vertical min-h-[60px]"
              rows={typeof value === 'object' ? 4 : 2}
            />
            <div className="flex space-x-2">
              <button
                onClick={handleSave}
                className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700"
              >
                Save
              </button>
              <button
                onClick={handleCancel}
                className="px-3 py-1 bg-gray-500 text-white text-sm rounded hover:bg-gray-600"
              >
                Cancel
              </button>
            </div>
          </div>
        ) : (
          <div className="text-gray-900 text-sm break-words">
            {typeof value === 'object' ? (
              <pre className="whitespace-pre-wrap font-mono text-xs bg-white p-2 rounded border">
                {JSON.stringify(value, null, 2)}
              </pre>
            ) : (
              <span className={displayValue ? '' : 'text-gray-400 italic'}>
                {displayValue || 'No value'}
              </span>
            )}
          </div>
        )}
      </div>
    );
  };

  const renderSection = (title: string, sectionData: any, sectionPath: string) => {
    if (!sectionData || typeof sectionData !== 'object') return null;

    // Get appropriate icon based on section type
    const getSectionIcon = (title: string) => {
      const iconMap: { [key: string]: string } = {
        'Company Information': 'M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4',
        'Seller Information': 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z',
        'Buyer Information': 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z',
        'Consignee Information': 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z',
        'Supplier Information': 'M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4',
        'Vendor Information': 'M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4',
        'Bank Details': 'M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z',
        'Tax Information': 'M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z',
        'Tax Details': 'M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z',
        'Taxes': 'M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z',
        'Delivery Details': 'M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4',
        'Dispatch Details': 'M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4',
        'Shipping Information': 'M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4',
        'Purchase Order Details': 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z',
        'Terms and Conditions': 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
      };

      return iconMap[title] || 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z';
    };

    // Get appropriate background color based on section type
    const getSectionStyle = (title: string) => {
      const styleMap: { [key: string]: string } = {
        'Company Information': 'bg-blue-50 border-blue-200',
        'Seller Information': 'bg-green-50 border-green-200',
        'Buyer Information': 'bg-purple-50 border-purple-200',
        'Consignee Information': 'bg-indigo-50 border-indigo-200',
        'Supplier Information': 'bg-orange-50 border-orange-200',
        'Vendor Information': 'bg-yellow-50 border-yellow-200',
        'Bank Details': 'bg-emerald-50 border-emerald-200',
        'Tax Information': 'bg-red-50 border-red-200',
        'Tax Details': 'bg-red-50 border-red-200',
        'Taxes': 'bg-red-50 border-red-200',
        'Delivery Details': 'bg-cyan-50 border-cyan-200',
        'Dispatch Details': 'bg-teal-50 border-teal-200',
        'Shipping Information': 'bg-sky-50 border-sky-200',
        'Purchase Order Details': 'bg-violet-50 border-violet-200',
        'Terms and Conditions': 'bg-slate-50 border-slate-200'
      };

      return styleMap[title] || 'bg-gray-50 border-gray-200';
    };

    return (
      <div className={`mb-6 border rounded-lg p-4 ${getSectionStyle(title)}`}>
        <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
          <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={getSectionIcon(title)} />
          </svg>
          {title}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Object.entries(sectionData).map(([key, value]) => {
            // Handle nested objects
            if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
              return (
                <div key={key} className="md:col-span-2 lg:col-span-3">
                  {renderSection(
                    getFieldDisplayName(key),
                    value,
                    `${sectionPath}.${key}`
                  )}
                </div>
              );
            }

            return (
              <div key={key}>
                {renderEditableField(
                  getFieldDisplayName(key),
                  value,
                  `${sectionPath}.${key}`
                )}
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const renderItemsTable = (items: any[], sectionPath: string) => {
    if (!items || !Array.isArray(items) || items.length === 0) return null;

    // Get field display names for table headers
    const getTableHeaderName = (key: string): string => {
      const headerNames: { [key: string]: string } = {
        'Description': 'Description',
        'HSN/SAC': 'HSN/SAC Code',
        'HSN_SAC': 'HSN/SAC Code',
        'HSN': 'HSN Code',
        'Quantity': 'Qty',
        'Unit': 'Unit',
        'Rate': 'Rate',
        'Amount': 'Amount',
        'Line': 'Line',
        'Line_No': 'Line No',
        'SKU': 'SKU',
        'Item_Code': 'Item Code',
        'Vendor_ID': 'Vendor ID',
        'Unit_Cost': 'Unit Cost',
        'Unit_Price': 'Unit Price',
        'Extended_Cost': 'Extended Cost',
        'Line_Total': 'Line Total',
        'GST_Rate': 'GST Rate (%)',
        'GST_Amount': 'GST Amount',
        'IGST': 'IGST',
        'Total_Line_Value': 'Total Value',
        'Need_By_Date': 'Need By',
        'Activity_End_Date': 'Activity End',
        'UOM': 'UOM',
        'Details': 'Details',
        'Tax': 'Tax'
      };

      return headerNames[key] || key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
    };

    // Format cell values for better display
    const formatCellValue = (key: string, value: any): string => {
      if (value === null || value === undefined) return '';

      // Format numbers with proper decimal places
      if (typeof value === 'number') {
        if (key.toLowerCase().includes('rate') || key.toLowerCase().includes('amount') || key.toLowerCase().includes('cost') || key.toLowerCase().includes('price')) {
          return value.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
        }
        if (key.toLowerCase().includes('quantity') || key.toLowerCase().includes('qty')) {
          return value.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
        }
        return value.toString();
      }

      // Handle objects
      if (typeof value === 'object') {
        return JSON.stringify(value);
      }

      return String(value);
    };

    const sectionTitle = sectionPath === 'Items' ? 'Line Items' :
                        sectionPath === 'Goods' ? 'Goods/Products' : 'Items';

    return (
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
          <svg className="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2V9a2 2 0 00-2-2H9V5z" />
          </svg>
          {sectionTitle} ({items.length} {items.length === 1 ? 'item' : 'items'})
        </h3>
        <div className="overflow-x-auto bg-white border border-gray-200 rounded-lg shadow-sm">
          <table className="min-w-full">
            <thead className="bg-gradient-to-r from-gray-50 to-gray-100">
              <tr>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider border-b">
                  #
                </th>
                {Object.keys(items[0] || {}).map((key) => (
                  <th key={key} className="px-4 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider border-b">
                    {getTableHeaderName(key)}
                  </th>
                ))}
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider border-b">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {items.map((item, index) => (
                <tr key={index} className="hover:bg-gray-50 transition-colors">
                  <td className="px-3 py-4 text-sm font-medium text-gray-900 border-b">
                    {index + 1}
                  </td>
                  {Object.entries(item).map(([key, value]) => (
                    <td key={key} className="px-4 py-4 text-sm text-gray-900 border-b">
                      <div className={`${key === 'Description' || key === 'Details' ? 'max-w-md' : 'max-w-xs'} ${
                        key === 'Description' ? '' : 'truncate'
                      }`} title={String(value)}>
                        {key === 'Description' ? (
                          <div className="whitespace-normal break-words">
                            {formatCellValue(key, value)}
                          </div>
                        ) : (
                          <span className={
                            (key.toLowerCase().includes('amount') || key.toLowerCase().includes('cost') || key.toLowerCase().includes('price') || key.toLowerCase().includes('rate')) && typeof value === 'number'
                              ? 'font-mono text-right block'
                              : ''
                          }>
                            {formatCellValue(key, value)}
                          </span>
                        )}
                      </div>
                    </td>
                  ))}
                  <td className="px-4 py-4 text-sm border-b">
                    <button
                      onClick={() => handleEdit(`${sectionPath}.${index}`, item)}
                      className="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded hover:bg-blue-100 hover:text-blue-700 transition-colors"
                    >
                      <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                      Edit
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Summary row for financial data */}
        {items.some(item => item.Amount || item.Extended_Cost || item.Line_Total) && (
          <div className="mt-3 bg-gray-50 border border-gray-200 rounded-lg p-3">
            <div className="flex justify-between items-center text-sm">
              <span className="font-medium text-gray-700">Summary:</span>
              <div className="flex space-x-6">
                {items.some(item => item.Quantity) && (
                  <span className="text-gray-600">
                    Total Qty: <span className="font-medium">
                      {items.reduce((sum, item) => sum + (parseFloat(item.Quantity) || 0), 0).toLocaleString('en-IN', { minimumFractionDigits: 2 })}
                    </span>
                  </span>
                )}
                {items.some(item => item.Amount) && (
                  <span className="text-gray-600">
                    Total Amount: <span className="font-medium text-green-600">
                      ₹{items.reduce((sum, item) => sum + (parseFloat(item.Amount) || 0), 0).toLocaleString('en-IN', { minimumFractionDigits: 2 })}
                    </span>
                  </span>
                )}
                {items.some(item => item.Extended_Cost) && (
                  <span className="text-gray-600">
                    Total Cost: <span className="font-medium text-green-600">
                      ₹{items.reduce((sum, item) => sum + (parseFloat(item.Extended_Cost) || 0), 0).toLocaleString('en-IN', { minimumFractionDigits: 2 })}
                    </span>
                  </span>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  if (viewMode === 'raw') {
    return (
      <div className="h-full overflow-auto p-4">
        <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm">
          <pre className="whitespace-pre-wrap">
            {JSON.stringify(data, null, 2)}
          </pre>
        </div>
      </div>
    );
  }

  // Helper function to check if a field should be excluded from UI
  const shouldExcludeField = (key: string): boolean => {
    const excludedFields = [
      'rawText', 'filename', 'fileSize', 'processedAt', 'documentType'
    ];
    return excludedFields.includes(key);
  };

  // Helper function to get field display name
  const getFieldDisplayName = (key: string): string => {
    const fieldNames: { [key: string]: string } = {
      'InvoiceNo': 'Invoice Number',
      'InvoiceDate': 'Invoice Date',
      'DeliveryNote': 'Delivery Note',
      'DeliveryNoteDate': 'Delivery Note Date',
      'DeliveryNoteNo': 'Delivery Note Number',
      'PurchaseOrderNo': 'Purchase Order Number',
      'IRN': 'Invoice Reference Number (IRN)',
      'AckNo': 'Acknowledgment Number',
      'AckDate': 'Acknowledgment Date',
      'TotalAmount': 'Total Amount',
      'AmountInWords': 'Amount in Words',
      'TotalQuantity': 'Total Quantity',
      'GSTIN': 'GST Identification Number',
      'PAN': 'PAN Number',
      'HSN/SAC': 'HSN/SAC Code',
      'Warranty': 'Warranty Period',
      'SupportEmail': 'Support Email',
      'Jurisdiction': 'Jurisdiction',
      'Date': 'Date',
      'Remarks': 'Remarks'
    };

    return fieldNames[key] || key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
  };

  return (
    <div className="h-full overflow-auto p-4 space-y-6">
      {/* Document Type Badge */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="px-3 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded-full">
            {data.documentType?.replace(/_/g, ' ').toUpperCase() || 'UNKNOWN'}
          </span>
          {data.processedAt && (
            <span className="text-xs text-gray-500">
              Processed: {new Date(data.processedAt).toLocaleString()}
            </span>
          )}
        </div>
      </div>

      {/* Document Header Information */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
          <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          Document Information
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {/* Invoice/Document Numbers */}
          {(data as any).InvoiceNo && renderEditableField('Invoice Number', (data as any).InvoiceNo, 'InvoiceNo')}
          {(data as any).InvoiceDate && renderEditableField('Invoice Date', (data as any).InvoiceDate, 'InvoiceDate')}
          {(data as any).DeliveryNote && renderEditableField('Delivery Note', (data as any).DeliveryNote, 'DeliveryNote')}
          {(data as any).DeliveryNoteDate && renderEditableField('Delivery Note Date', (data as any).DeliveryNoteDate, 'DeliveryNoteDate')}
          {(data as any).DeliveryNoteNo && renderEditableField('Delivery Note Number', (data as any).DeliveryNoteNo, 'DeliveryNoteNo')}
          {(data as any).PurchaseOrderNo && renderEditableField('Purchase Order Number', (data as any).PurchaseOrderNo, 'PurchaseOrderNo')}
          {(data as any).Date && renderEditableField('Date', (data as any).Date, 'Date')}

          {/* E-Invoice Details */}
          {(data as any).IRN && renderEditableField('Invoice Reference Number (IRN)', (data as any).IRN, 'IRN')}
          {(data as any).AckNo && renderEditableField('Acknowledgment Number', (data as any).AckNo, 'AckNo')}
          {(data as any).AckDate && renderEditableField('Acknowledgment Date', (data as any).AckDate, 'AckDate')}
        </div>
      </div>

      {/* Seller Information */}
      {(data as any).Seller && renderSection('Seller Information', (data as any).Seller, 'Seller')}

      {/* Company Information */}
      {(data as any).Company && renderSection('Company Information', (data as any).Company, 'Company')}

      {/* Job Order Information */}
      {(data as any).JobOrder && renderSection('Job Order Information', (data as any).JobOrder, 'JobOrder')}

      {/* Buyer Information */}
      {(data as any).Buyer && renderSection('Buyer Information', (data as any).Buyer, 'Buyer')}

      {/* Consignee Information */}
      {(data as any).Consignee && renderSection('Consignee Information', (data as any).Consignee, 'Consignee')}

      {/* Supplier Information */}
      {(data as any).Supplier && renderSection('Supplier Information', (data as any).Supplier, 'Supplier')}

      {/* Vendor Information */}
      {(data as any).Vendor && renderSection('Vendor Information', (data as any).Vendor, 'Vendor')}

      {/* Purchase Order Details */}
      {(data as any).PurchaseOrder && renderSection('Purchase Order Details', (data as any).PurchaseOrder, 'PurchaseOrder')}

      {/* Delivery Details */}
      {(data as any).DeliveryDetails && renderSection('Delivery Details', (data as any).DeliveryDetails, 'DeliveryDetails')}

      {/* Dispatch Details */}
      {(data as any).DispatchDetails && renderSection('Dispatch Details', (data as any).DispatchDetails, 'DispatchDetails')}

      {/* Shipping Information */}
      {(data as any).Shipping && renderSection('Shipping Information', (data as any).Shipping, 'Shipping')}

      {/* Items/Goods Tables */}
      {(data as any).Items && renderItemsTable((data as any).Items, 'Items')}
      {(data as any).Goods && renderItemsTable((data as any).Goods, 'Goods')}

      {/* Tax Information */}
      {(data as any).Tax && renderSection('Tax Information', (data as any).Tax, 'Tax')}
      {(data as any).TaxDetails && renderSection('Tax Details', (data as any).TaxDetails, 'TaxDetails')}
      {(data as any).Taxes && renderSection('Taxes', (data as any).Taxes, 'Taxes')}

      {/* Financial Information */}
      <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
          <svg className="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
          </svg>
          Financial Summary
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {(data as any).TotalAmount && renderEditableField('Total Amount', (data as any).TotalAmount, 'TotalAmount')}
          {(data as any).AmountInWords && renderEditableField('Amount in Words', (data as any).AmountInWords, 'AmountInWords')}
          {(data as any).TotalQuantity && renderEditableField('Total Quantity', (data as any).TotalQuantity, 'TotalQuantity')}
        </div>
      </div>

      {/* Totals Information */}
      {(data as any).Totals && renderSection('Totals', (data as any).Totals, 'Totals')}

      {/* Bank Details */}
      {(data as any).BankDetails && renderSection('Bank Details', (data as any).BankDetails, 'BankDetails')}

      {/* Terms and Conditions */}
      {(data as any).Terms && renderSection('Terms and Conditions', (data as any).Terms, 'Terms')}

      {/* Portal Information */}
      {(data as any).Portal_Info && renderSection('Portal Information', (data as any).Portal_Info, 'Portal_Info')}

      {/* Notes */}
      {(data as any).Notes && Array.isArray((data as any).Notes) && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
            <svg className="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Notes & Instructions
          </h3>
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <ul className="list-disc list-inside space-y-2">
              {(data as any).Notes.map((note: string, index: number) => (
                <li key={index} className="text-sm text-gray-700">{note}</li>
              ))}
            </ul>
          </div>
        </div>
      )}

      {/* Document Information */}
      {(data as any).Document && renderSection('Document Information', (data as any).Document, 'Document')}

      {/* Additional Fields */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
          <svg className="w-5 h-5 text-gray-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          Additional Information
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {(data as any).Warranty && renderEditableField('Warranty Period', (data as any).Warranty, 'Warranty')}
          {(data as any).SupportEmail && renderEditableField('Support Email', (data as any).SupportEmail, 'SupportEmail')}
          {(data as any).Jurisdiction && renderEditableField('Jurisdiction', (data as any).Jurisdiction, 'Jurisdiction')}
          {(data as any).Remarks && renderEditableField('Remarks', (data as any).Remarks, 'Remarks')}
          {(data as any).AuthorizedBy && renderEditableField('Authorized By', (data as any).AuthorizedBy, 'AuthorizedBy')}
          {(data as any).DocumentNote && renderEditableField('Document Note', (data as any).DocumentNote, 'DocumentNote')}
          {(data as any).Signature && renderEditableField('Signature', (data as any).Signature, 'Signature')}
          {(data as any).Condition && renderEditableField('Condition', (data as any).Condition, 'Condition')}
          {(data as any).E_O_E !== undefined && renderEditableField('E.O.E', (data as any).E_O_E, 'E_O_E')}
          {(data as any).DeliveryChallan && renderEditableField('Delivery Challan', (data as any).DeliveryChallan, 'DeliveryChallan')}
        </div>
      </div>

      {/* Dynamic Fields - Show any remaining fields that aren't explicitly handled */}
      {(() => {
        const handledFields = [
          'documentType', 'rawText', 'filename', 'fileSize', 'processedAt',
          'InvoiceNo', 'InvoiceDate', 'DeliveryNote', 'DeliveryNoteDate', 'DeliveryNoteNo',
          'PurchaseOrderNo', 'Date', 'IRN', 'AckNo', 'AckDate', 'Seller', 'Company', 'JobOrder',
          'Buyer', 'Consignee', 'Supplier', 'Vendor', 'PurchaseOrder', 'DeliveryDetails',
          'DispatchDetails', 'Shipping', 'Items', 'Goods', 'Tax', 'TaxDetails', 'Taxes',
          'TotalAmount', 'AmountInWords', 'TotalQuantity', 'Totals', 'BankDetails', 'Terms',
          'Portal_Info', 'Notes', 'Document', 'Warranty', 'SupportEmail', 'Jurisdiction',
          'Remarks', 'AuthorizedBy', 'DocumentNote', 'Signature', 'Condition', 'E_O_E',
          'DeliveryChallan'
        ];

        const remainingFields = Object.entries(data).filter(([key, value]) =>
          !handledFields.includes(key) &&
          !shouldExcludeField(key) &&
          value !== null &&
          value !== undefined &&
          value !== ''
        );

        if (remainingFields.length > 0) {
          return (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                </svg>
                Other Fields
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {remainingFields.map(([key, value]) => (
                  <div key={key}>
                    {typeof value === 'object' && value !== null ?
                      renderSection(getFieldDisplayName(key), value, key) :
                      renderEditableField(getFieldDisplayName(key), value, key)
                    }
                  </div>
                ))}
              </div>
            </div>
          );
        }
        return null;
      })()}
    </div>
  );
}