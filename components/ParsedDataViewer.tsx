'use client';

import { useState } from 'react';
import { ParsedDocumentData } from '@/lib/advanced-pdf-parser';

interface ParsedDataViewerProps {
  data: ParsedDocumentData;
  viewMode: 'ui' | 'raw';
  onUpdate: (updatedData: ParsedDocumentData) => void;
}

export default function ParsedDataViewer({ data, viewMode, onUpdate }: ParsedDataViewerProps) {
  const [editingField, setEditingField] = useState<string | null>(null);
  const [editValue, setEditValue] = useState<string>('');

  const handleEdit = (fieldPath: string, currentValue: any) => {
    setEditingField(fieldPath);
    setEditValue(typeof currentValue === 'object' ? JSON.stringify(currentValue, null, 2) : String(currentValue || ''));
  };

  const handleSave = () => {
    if (!editingField) return;

    try {
      // Parse the field path and update the data
      const pathParts = editingField.split('.');
      const updatedData = { ...data };
      let current: any = updatedData;

      // Navigate to the parent object
      for (let i = 0; i < pathParts.length - 1; i++) {
        if (!current[pathParts[i]]) {
          current[pathParts[i]] = {};
        }
        current = current[pathParts[i]];
      }

      // Set the final value
      const finalKey = pathParts[pathParts.length - 1];
      try {
        // Try to parse as JSON first
        current[finalKey] = JSON.parse(editValue);
      } catch {
        // If not valid JSON, treat as string
        current[finalKey] = editValue;
      }

      onUpdate(updatedData);
      setEditingField(null);
      setEditValue('');
    } catch (error) {
      console.error('Error updating field:', error);
    }
  };

  const handleCancel = () => {
    setEditingField(null);
    setEditValue('');
  };

  const renderEditableField = (label: string, value: any, fieldPath: string) => {
    const isEditing = editingField === fieldPath;
    const displayValue = typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value || '');

    return (
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 hover:bg-gray-100 transition-colors">
        <div className="flex items-center justify-between mb-2">
          <label className="font-medium text-gray-700 text-sm">{label}</label>
          {!isEditing && (
            <button
              onClick={() => handleEdit(fieldPath, value)}
              className="text-blue-600 hover:text-blue-800 text-sm flex items-center space-x-1"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              <span>Edit</span>
            </button>
          )}
        </div>
        
        {isEditing ? (
          <div className="space-y-2">
            <textarea
              value={editValue}
              onChange={(e) => setEditValue(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md text-sm font-mono resize-vertical min-h-[60px]"
              rows={typeof value === 'object' ? 4 : 2}
            />
            <div className="flex space-x-2">
              <button
                onClick={handleSave}
                className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700"
              >
                Save
              </button>
              <button
                onClick={handleCancel}
                className="px-3 py-1 bg-gray-500 text-white text-sm rounded hover:bg-gray-600"
              >
                Cancel
              </button>
            </div>
          </div>
        ) : (
          <div className="text-gray-900 text-sm break-words">
            {typeof value === 'object' ? (
              <pre className="whitespace-pre-wrap font-mono text-xs bg-white p-2 rounded border">
                {JSON.stringify(value, null, 2)}
              </pre>
            ) : (
              <span className={displayValue ? '' : 'text-gray-400 italic'}>
                {displayValue || 'No value'}
              </span>
            )}
          </div>
        )}
      </div>
    );
  };

  const renderSection = (title: string, sectionData: any, sectionPath: string) => {
    if (!sectionData || typeof sectionData !== 'object') return null;

    return (
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
          <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          {title}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(sectionData).map(([key, value]) => (
            <div key={key}>
              {renderEditableField(
                key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()),
                value,
                `${sectionPath}.${key}`
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderItemsTable = (items: any[], sectionPath: string) => {
    if (!items || !Array.isArray(items) || items.length === 0) return null;

    return (
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
          <svg className="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2V9a2 2 0 00-2-2H9V5z" />
          </svg>
          Items ({items.length})
        </h3>
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white border border-gray-200 rounded-lg">
            <thead className="bg-gray-50">
              <tr>
                {Object.keys(items[0] || {}).map((key) => (
                  <th key={key} className="px-4 py-2 text-left text-sm font-medium text-gray-700 border-b">
                    {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                  </th>
                ))}
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-700 border-b">Actions</th>
              </tr>
            </thead>
            <tbody>
              {items.map((item, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  {Object.entries(item).map(([key, value]) => (
                    <td key={key} className="px-4 py-2 text-sm text-gray-900 border-b">
                      <div className="max-w-xs truncate" title={String(value)}>
                        {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                      </div>
                    </td>
                  ))}
                  <td className="px-4 py-2 text-sm border-b">
                    <button
                      onClick={() => handleEdit(`${sectionPath}.${index}`, item)}
                      className="text-blue-600 hover:text-blue-800 text-xs"
                    >
                      Edit
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  if (viewMode === 'raw') {
    return (
      <div className="h-full overflow-auto p-4">
        <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm">
          <pre className="whitespace-pre-wrap">
            {JSON.stringify(data, null, 2)}
          </pre>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full overflow-auto p-4 space-y-6">
      {/* Document Type Badge */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="px-3 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded-full">
            {data.documentType?.replace(/_/g, ' ').toUpperCase() || 'UNKNOWN'}
          </span>
          {data.processedAt && (
            <span className="text-xs text-gray-500">
              Processed: {new Date(data.processedAt).toLocaleString()}
            </span>
          )}
        </div>
      </div>

      {/* Company Information */}
      {data.Company && renderSection('Company Information', data.Company, 'Company')}

      {/* Consignee Information */}
      {data.Consignee && renderSection('Consignee Information', data.Consignee, 'Consignee')}

      {/* Buyer Information */}
      {data.Buyer && renderSection('Buyer Information', data.Buyer, 'Buyer')}

      {/* Delivery Details */}
      {data.DeliveryDetails && renderSection('Delivery Details', data.DeliveryDetails, 'DeliveryDetails')}

      {/* Items/Goods */}
      {data.Goods && renderItemsTable(data.Goods, 'Goods')}
      {data.Items && renderItemsTable(data.Items, 'Items')}

      {/* Purchase Order Details */}
      {data.PurchaseOrder && renderSection('Purchase Order Details', data.PurchaseOrder, 'PurchaseOrder')}

      {/* Tax Details */}
      {data.TaxDetails && renderSection('Tax Details', data.TaxDetails, 'TaxDetails')}

      {/* Bank Details */}
      {data.BankDetails && renderSection('Bank Details', data.BankDetails, 'BankDetails')}

      {/* Additional Fields */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {data.TotalAmount && renderEditableField('Total Amount', data.TotalAmount, 'TotalAmount')}
        {data.AmountInWords && renderEditableField('Amount in Words', data.AmountInWords, 'AmountInWords')}
        {data.TotalQuantity && renderEditableField('Total Quantity', data.TotalQuantity, 'TotalQuantity')}
        {data.IRN && renderEditableField('IRN', data.IRN, 'IRN')}
        {data.AckNo && renderEditableField('Acknowledgment Number', data.AckNo, 'AckNo')}
        {data.AckDate && renderEditableField('Acknowledgment Date', data.AckDate, 'AckDate')}
      </div>
    </div>
  );
}