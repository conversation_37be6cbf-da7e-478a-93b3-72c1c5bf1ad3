'use client';

import React, { useState, useRef } from 'react';
import { ParsedDocumentData } from '@/lib/enhanced-parser';
import PDFViewer from './PDFViewer';
import ParsedDataViewer from './ParsedDataViewer';
import TopMenuBar from './TopMenuBar';

interface SplitScreenViewerProps {
  pdfFile: File;
  parsedData: ParsedDocumentData;
  onAddNewPDF: () => void;
  onUpdateData: (updatedData: ParsedDocumentData) => void;
}

export default function SplitScreenViewer({ 
  pdfFile, 
  parsedData, 
  onAddNewPDF, 
  onUpdateData 
}: SplitScreenViewerProps) {
  const [viewMode, setViewMode] = useState<'ui' | 'raw'>('ui');
  const [isResizing, setIsResizing] = useState(false);
  const [leftWidth, setLeftWidth] = useState(50); // Percentage
  const [containerWidth, setContainerWidth] = useState(0);
  const [isMinimized, setIsMinimized] = useState(false);
  const [previousWidth, setPreviousWidth] = useState(50);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleMouseDown = (e: React.MouseEvent) => {
    if (!isMinimized) {
      setIsResizing(true);
      e.preventDefault();
    }
  };

  const handleMinimize = () => {
    if (isMinimized) {
      // Expand back to previous width
      setLeftWidth(previousWidth);
      setIsMinimized(false);
    } else {
      // Minimize to icon width
      setPreviousWidth(leftWidth);
      setLeftWidth(4); // Small width for icon sidebar
      setIsMinimized(true);
    }
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isResizing || !containerRef.current || isMinimized) return;

    const containerRect = containerRef.current.getBoundingClientRect();
    const newLeftWidth = ((e.clientX - containerRect.left) / containerRect.width) * 100;

    // Constrain between 20% and 80%
    const constrainedWidth = Math.max(20, Math.min(80, newLeftWidth));
    setLeftWidth(constrainedWidth);

    // Update container width for PDF viewer
    setContainerWidth(containerRect.width * (constrainedWidth / 100));
  };

  const handleMouseUp = () => {
    setIsResizing(false);
  };

  // Add event listeners for mouse move and up
  React.useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };
  }, [isResizing]);

  // Initialize container width
  React.useEffect(() => {
    if (containerRef.current) {
      const containerRect = containerRef.current.getBoundingClientRect();
      setContainerWidth(containerRect.width * (leftWidth / 100));
    }
  }, [leftWidth]);

  // Keyboard shortcut for minimize/expand (Ctrl + M)
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.key === 'm') {
        e.preventDefault();
        handleMinimize();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isMinimized, leftWidth, previousWidth]);

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Top Menu Bar */}
      <TopMenuBar onAddNewPDF={onAddNewPDF} />

      {/* Split Screen Container */}
      <div
        ref={containerRef}
        className="flex-1 flex overflow-hidden"
        style={{ height: 'calc(100vh - 64px)' }}
      >
        {/* Left Panel - PDF Viewer or Minimized Sidebar */}
        <div
          className="bg-white border-r border-gray-300 flex flex-col transition-all duration-300 ease-in-out"
          style={{ width: `${leftWidth}%` }}
        >
          {isMinimized ? (
            /* Minimized Icon Sidebar */
            <div className="flex flex-col items-center py-4 space-y-4 h-full">
              <button
                onClick={handleMinimize}
                className="p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-200 shadow-lg group hover:shadow-xl"
                title="Expand PDF Viewer (Ctrl+M)"
              >
                <svg className="w-6 h-6 transform group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </button>

              <div className="flex flex-col items-center space-y-3 text-xs text-gray-500 text-center">
                <div className="w-6 h-1 bg-gradient-to-r from-blue-400 to-purple-400 rounded animate-pulse"></div>
                <div className="transform -rotate-90 whitespace-nowrap">
                  <span className="text-xs font-medium">PDF</span>
                </div>
                <div className="w-6 h-1 bg-gradient-to-r from-purple-400 to-blue-400 rounded animate-pulse"></div>
              </div>

              <div className="flex flex-col items-center space-y-2 text-xs text-gray-400 text-center mt-auto mb-4">
                <svg className="w-4 h-4 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div className="transform -rotate-90 whitespace-nowrap">
                  <span className="text-xs">{(pdfFile.size / 1024).toFixed(0)}KB</span>
                </div>
              </div>

              {/* Hint for expansion */}
              <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2">
                <div className="w-1 h-8 bg-gradient-to-t from-blue-400 to-transparent rounded-full opacity-50"></div>
              </div>
            </div>
          ) : (
            /* Full PDF Viewer */
            <>
              <div className="bg-gray-100 px-4 py-2 border-b border-gray-200 flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <h3 className="font-semibold text-gray-800">PDF Document</h3>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="text-sm text-gray-600">
                    {pdfFile.name} ({(pdfFile.size / 1024).toFixed(1)} KB)
                  </div>
                  <button
                    onClick={handleMinimize}
                    className="p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-200 rounded transition-colors group"
                    title="Minimize PDF Viewer (Ctrl+M)"
                  >
                    <svg className="w-4 h-4 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                    </svg>
                  </button>
                </div>
              </div>
              <div className="flex-1 overflow-hidden">
                <PDFViewer file={pdfFile} containerWidth={containerWidth} />
              </div>
            </>
          )}
        </div>

        {/* Resizer - Hidden when minimized */}
        {!isMinimized && (
          <div
            className={`w-1 bg-gray-300 hover:bg-blue-500 cursor-col-resize transition-colors ${
              isResizing ? 'bg-blue-500' : ''
            }`}
            onMouseDown={handleMouseDown}
          >
            <div className="h-full w-full flex items-center justify-center">
              <div className="w-0.5 h-8 bg-gray-400 rounded-full"></div>
            </div>
          </div>
        )}

        {/* Right Panel - Parsed Data */}
        <div
          className="bg-white flex flex-col transition-all duration-300 ease-in-out"
          style={{ width: isMinimized ? `${100 - leftWidth}%` : `${100 - leftWidth}%` }}
        >
          <div className="bg-gray-100 px-4 py-2 border-b border-gray-200 flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h3 className="font-semibold text-gray-800">Parsed Data</h3>
            </div>
            
            {/* View Mode Toggle */}
            <div className="flex bg-white rounded-lg p-1 border border-gray-300">
              <button
                onClick={() => setViewMode('ui')}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  viewMode === 'ui'
                    ? 'bg-blue-600 text-white shadow-sm'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <div className="flex items-center space-x-1">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  <span>UI View</span>
                </div>
              </button>
              <button
                onClick={() => setViewMode('raw')}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  viewMode === 'raw'
                    ? 'bg-blue-600 text-white shadow-sm'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <div className="flex items-center space-x-1">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                  </svg>
                  <span>Raw JSON</span>
                </div>
              </button>
            </div>
          </div>
          
          <div className="flex-1 overflow-hidden">
            <ParsedDataViewer 
              data={parsedData}
              viewMode={viewMode}
              onUpdate={onUpdateData}
            />
          </div>
        </div>
      </div>
    </div>
  );
}