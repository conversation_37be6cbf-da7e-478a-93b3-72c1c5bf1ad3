'use client';

import { useState, useRef } from 'react';
import { ParsedDocumentData } from '@/lib/advanced-pdf-parser';
import SplitScreenViewer from './SplitScreenViewer';

interface FileUploadProps {
  onParsed?: (data: ParsedDocumentData) => void;
}

export default function FileUpload({ onParsed }: FileUploadProps) {
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [parsedData, setParsedData] = useState<ParsedDocumentData | null>(null);
  const [error, setError] = useState<{ message: string; details?: string } | null>(null);
  const [showSplitScreen, setShowSplitScreen] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0] || null;
    setFile(selectedFile);
    setError(null);
    setParsedData(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!file) {
      setError({ message: 'Please select a PDF file' });
      return;
    }

    if (!file.name.endsWith('.pdf')) {
      setError({ message: 'Only PDF files are supported' });
      return;
    }

    setIsUploading(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/parse-pdf', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to parse PDF', { 
          cause: data.details 
        });
      }

      // Set the parsed data and show split screen
      setParsedData(data.document);
      setShowSplitScreen(true);

      if (onParsed) {
        onParsed(data.document);
      }
    } catch (err) {
      console.error('Error parsing PDF:', err);
      setError({ 
        message: err instanceof Error ? err.message : 'Failed to parse PDF',
        details: err instanceof Error ? err.cause as string : undefined
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleReset = () => {
    setFile(null);
    setParsedData(null);
    setError(null);
    setShowSplitScreen(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleAddNewPDF = () => {
    handleReset();
  };

  const handleUpdateData = (updatedData: ParsedDocumentData) => {
    setParsedData(updatedData);
    if (onParsed) {
      onParsed(updatedData);
    }
  };



  // Show split screen if we have both file and parsed data
  if (showSplitScreen && file && parsedData) {
    return (
      <SplitScreenViewer
        pdfFile={file}
        parsedData={parsedData}
        onAddNewPDF={handleAddNewPDF}
        onUpdateData={handleUpdateData}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-6xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-2">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">PDF Invoice Parser</h1>
                <p className="text-sm text-gray-600">Extract and edit invoice data from PDFs</p>
              </div>
            </div>
            <div className="flex items-center space-x-4 text-xs text-gray-500">
              <div className="flex items-center">
                <svg className="w-3 h-3 text-green-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Fast Processing
              </div>
              <div className="flex items-center">
                <svg className="w-3 h-3 text-green-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Secure & Private
              </div>
              <div className="flex items-center">
                <svg className="w-3 h-3 text-green-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                No File Storage
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="w-full max-w-6xl mx-auto p-6">
      {/* Upload Section */}
      <div className="bg-white border-2 border-dashed border-gray-300 rounded-xl p-8 mb-8 hover:border-blue-400 transition-colors">
        <form onSubmit={handleSubmit}>
          <div className="text-center mb-6">
            <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
              <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Upload PDF File</h3>
            <p className="text-gray-600">Select a PDF file to extract and analyze its content</p>
          </div>

          <div className="mb-6">
            <input
              ref={fileInputRef}
              type="file"
              id="pdf-file"
              accept=".pdf"
              onChange={handleFileChange}
              className="block w-full text-sm text-gray-700
                        file:mr-4 file:py-3 file:px-6
                        file:rounded-lg file:border-0
                        file:text-sm file:font-medium
                        file:bg-blue-600 file:text-white
                        hover:file:bg-blue-700 file:cursor-pointer
                        cursor-pointer border border-gray-300 rounded-lg
                        focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {file && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <div className="flex items-center">
                <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <div>
                  <p className="text-sm font-medium text-blue-900">{file.name}</p>
                  <p className="text-xs text-blue-700">Size: {(file.size / 1024).toFixed(2)} KB</p>
                </div>
              </div>
            </div>
          )}

          <div className="flex justify-center space-x-4">
            <button
              type="submit"
              disabled={!file || isUploading}
              className="px-8 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700
                        focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                        disabled:opacity-50 disabled:cursor-not-allowed transition-colors
                        flex items-center space-x-2"
            >
              {isUploading ? (
                <>
                  <svg className="animate-spin w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span>Parsing...</span>
                </>
              ) : (
                <>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  <span>Parse PDF</span>
                </>
              )}
            </button>

            <button
              type="button"
              onClick={handleReset}
              className="px-6 py-3 bg-gray-100 text-gray-700 font-medium rounded-lg hover:bg-gray-200
                        focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2
                        transition-colors"
            >
              Reset
            </button>
          </div>
        </form>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-xl p-6 mb-8">
          <div className="flex items-start">
            <svg className="w-6 h-6 text-red-600 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-red-900 mb-2">Error Processing PDF</h3>
              <p className="text-red-800 mb-2">{error.message}</p>
              {error.details && (
                <div className="bg-red-100 border border-red-200 rounded-lg p-3 mt-3">
                  <p className="text-sm text-red-700">
                    <span className="font-medium">Technical Details: </span>
                    {error.details}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white/80 backdrop-blur-sm border-t border-gray-200 mt-16">
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <p className="text-gray-600 mb-2">
              PDF Parser Application - Built with Next.js & TypeScript
            </p>
            <p className="text-sm text-gray-500">
              Powered by enhanced PDF parsing with split-screen editing
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}